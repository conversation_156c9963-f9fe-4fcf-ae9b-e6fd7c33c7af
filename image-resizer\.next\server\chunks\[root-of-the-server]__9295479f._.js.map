{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/NukeXDs/ImageResizer/image-resizer/src/app/api/resize/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport sharp from 'sharp';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const formData = await request.formData();\n    const file = formData.get('file') as File;\n    const width = parseInt(formData.get('width') as string) || undefined;\n    const height = parseInt(formData.get('height') as string) || undefined;\n    const quality = parseInt(formData.get('quality') as string) || 80;\n    const format = formData.get('format') as string || 'jpeg';\n    const targetSize = parseInt(formData.get('targetSize') as string) || undefined; // in KB\n\n    if (!file) {\n      return NextResponse.json({ error: 'No file provided' }, { status: 400 });\n    }\n\n    const buffer = Buffer.from(await file.arrayBuffer());\n    let sharpInstance = sharp(buffer);\n\n    // Get original metadata\n    const metadata = await sharpInstance.metadata();\n    \n    // Resize if dimensions provided\n    if (width || height) {\n      sharpInstance = sharpInstance.resize(width, height, {\n        fit: 'inside',\n        withoutEnlargement: true\n      });\n    }\n\n    // Convert format and apply quality\n    let outputBuffer: Buffer;\n    switch (format.toLowerCase()) {\n      case 'jpeg':\n      case 'jpg':\n        outputBuffer = await sharpInstance.jpeg({ quality }).toBuffer();\n        break;\n      case 'png':\n        outputBuffer = await sharpInstance.png({ quality: Math.round(quality / 10) }).toBuffer();\n        break;\n      case 'webp':\n        outputBuffer = await sharpInstance.webp({ quality }).toBuffer();\n        break;\n      case 'avif':\n        outputBuffer = await sharpInstance.avif({ quality }).toBuffer();\n        break;\n      default:\n        outputBuffer = await sharpInstance.jpeg({ quality }).toBuffer();\n    }\n\n    // If target size is specified, iteratively reduce quality\n    if (targetSize) {\n      const targetBytes = targetSize * 1024;\n      let currentQuality = quality;\n      \n      while (outputBuffer.length > targetBytes && currentQuality > 10) {\n        currentQuality -= 10;\n        \n        switch (format.toLowerCase()) {\n          case 'jpeg':\n          case 'jpg':\n            outputBuffer = await sharp(buffer)\n              .resize(width, height, { fit: 'inside', withoutEnlargement: true })\n              .jpeg({ quality: currentQuality })\n              .toBuffer();\n            break;\n          case 'png':\n            outputBuffer = await sharp(buffer)\n              .resize(width, height, { fit: 'inside', withoutEnlargement: true })\n              .png({ quality: Math.round(currentQuality / 10) })\n              .toBuffer();\n            break;\n          case 'webp':\n            outputBuffer = await sharp(buffer)\n              .resize(width, height, { fit: 'inside', withoutEnlargement: true })\n              .webp({ quality: currentQuality })\n              .toBuffer();\n            break;\n          case 'avif':\n            outputBuffer = await sharp(buffer)\n              .resize(width, height, { fit: 'inside', withoutEnlargement: true })\n              .avif({ quality: currentQuality })\n              .toBuffer();\n            break;\n        }\n      }\n    }\n\n    const outputMetadata = await sharp(outputBuffer).metadata();\n\n    return new NextResponse(outputBuffer, {\n      headers: {\n        'Content-Type': `image/${format}`,\n        'Content-Disposition': `attachment; filename=\"resized.${format}\"`,\n        'X-Original-Size': metadata.size?.toString() || '0',\n        'X-Output-Size': outputBuffer.length.toString(),\n        'X-Original-Width': metadata.width?.toString() || '0',\n        'X-Original-Height': metadata.height?.toString() || '0',\n        'X-Output-Width': outputMetadata.width?.toString() || '0',\n        'X-Output-Height': outputMetadata.height?.toString() || '0',\n      },\n    });\n  } catch (error) {\n    console.error('Error processing image:', error);\n    return NextResponse.json({ error: 'Failed to process image' }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,QAAQ,QAAQ;QACvC,MAAM,OAAO,SAAS,GAAG,CAAC;QAC1B,MAAM,QAAQ,SAAS,SAAS,GAAG,CAAC,aAAuB;QAC3D,MAAM,SAAS,SAAS,SAAS,GAAG,CAAC,cAAwB;QAC7D,MAAM,UAAU,SAAS,SAAS,GAAG,CAAC,eAAyB;QAC/D,MAAM,SAAS,SAAS,GAAG,CAAC,aAAuB;QACnD,MAAM,aAAa,SAAS,SAAS,GAAG,CAAC,kBAA4B,WAAW,QAAQ;QAExF,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAmB,GAAG;gBAAE,QAAQ;YAAI;QACxE;QAEA,MAAM,SAAS,OAAO,IAAI,CAAC,MAAM,KAAK,WAAW;QACjD,IAAI,gBAAgB,CAAA,GAAA,mGAAA,CAAA,UAAK,AAAD,EAAE;QAE1B,wBAAwB;QACxB,MAAM,WAAW,MAAM,cAAc,QAAQ;QAE7C,gCAAgC;QAChC,IAAI,SAAS,QAAQ;YACnB,gBAAgB,cAAc,MAAM,CAAC,OAAO,QAAQ;gBAClD,KAAK;gBACL,oBAAoB;YACtB;QACF;QAEA,mCAAmC;QACnC,IAAI;QACJ,OAAQ,OAAO,WAAW;YACxB,KAAK;YACL,KAAK;gBACH,eAAe,MAAM,cAAc,IAAI,CAAC;oBAAE;gBAAQ,GAAG,QAAQ;gBAC7D;YACF,KAAK;gBACH,eAAe,MAAM,cAAc,GAAG,CAAC;oBAAE,SAAS,KAAK,KAAK,CAAC,UAAU;gBAAI,GAAG,QAAQ;gBACtF;YACF,KAAK;gBACH,eAAe,MAAM,cAAc,IAAI,CAAC;oBAAE;gBAAQ,GAAG,QAAQ;gBAC7D;YACF,KAAK;gBACH,eAAe,MAAM,cAAc,IAAI,CAAC;oBAAE;gBAAQ,GAAG,QAAQ;gBAC7D;YACF;gBACE,eAAe,MAAM,cAAc,IAAI,CAAC;oBAAE;gBAAQ,GAAG,QAAQ;QACjE;QAEA,0DAA0D;QAC1D,IAAI,YAAY;YACd,MAAM,cAAc,aAAa;YACjC,IAAI,iBAAiB;YAErB,MAAO,aAAa,MAAM,GAAG,eAAe,iBAAiB,GAAI;gBAC/D,kBAAkB;gBAElB,OAAQ,OAAO,WAAW;oBACxB,KAAK;oBACL,KAAK;wBACH,eAAe,MAAM,CAAA,GAAA,mGAAA,CAAA,UAAK,AAAD,EAAE,QACxB,MAAM,CAAC,OAAO,QAAQ;4BAAE,KAAK;4BAAU,oBAAoB;wBAAK,GAChE,IAAI,CAAC;4BAAE,SAAS;wBAAe,GAC/B,QAAQ;wBACX;oBACF,KAAK;wBACH,eAAe,MAAM,CAAA,GAAA,mGAAA,CAAA,UAAK,AAAD,EAAE,QACxB,MAAM,CAAC,OAAO,QAAQ;4BAAE,KAAK;4BAAU,oBAAoB;wBAAK,GAChE,GAAG,CAAC;4BAAE,SAAS,KAAK,KAAK,CAAC,iBAAiB;wBAAI,GAC/C,QAAQ;wBACX;oBACF,KAAK;wBACH,eAAe,MAAM,CAAA,GAAA,mGAAA,CAAA,UAAK,AAAD,EAAE,QACxB,MAAM,CAAC,OAAO,QAAQ;4BAAE,KAAK;4BAAU,oBAAoB;wBAAK,GAChE,IAAI,CAAC;4BAAE,SAAS;wBAAe,GAC/B,QAAQ;wBACX;oBACF,KAAK;wBACH,eAAe,MAAM,CAAA,GAAA,mGAAA,CAAA,UAAK,AAAD,EAAE,QACxB,MAAM,CAAC,OAAO,QAAQ;4BAAE,KAAK;4BAAU,oBAAoB;wBAAK,GAChE,IAAI,CAAC;4BAAE,SAAS;wBAAe,GAC/B,QAAQ;wBACX;gBACJ;YACF;QACF;QAEA,MAAM,iBAAiB,MAAM,CAAA,GAAA,mGAAA,CAAA,UAAK,AAAD,EAAE,cAAc,QAAQ;QAEzD,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,cAAc;YACpC,SAAS;gBACP,gBAAgB,CAAC,MAAM,EAAE,QAAQ;gBACjC,uBAAuB,CAAC,8BAA8B,EAAE,OAAO,CAAC,CAAC;gBACjE,mBAAmB,SAAS,IAAI,EAAE,cAAc;gBAChD,iBAAiB,aAAa,MAAM,CAAC,QAAQ;gBAC7C,oBAAoB,SAAS,KAAK,EAAE,cAAc;gBAClD,qBAAqB,SAAS,MAAM,EAAE,cAAc;gBACpD,kBAAkB,eAAe,KAAK,EAAE,cAAc;gBACtD,mBAAmB,eAAe,MAAM,EAAE,cAAc;YAC1D;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAA0B,GAAG;YAAE,QAAQ;QAAI;IAC/E;AACF", "debugId": null}}]}