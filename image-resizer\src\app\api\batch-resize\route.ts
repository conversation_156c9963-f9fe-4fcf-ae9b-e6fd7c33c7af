import { NextRequest, NextResponse } from 'next/server';
import sharp from 'sharp';
import J<PERSON><PERSON><PERSON> from 'jszip';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const files = formData.getAll('files') as File[];
    const width = parseInt(formData.get('width') as string) || undefined;
    const height = parseInt(formData.get('height') as string) || undefined;
    const quality = parseInt(formData.get('quality') as string) || 80;
    const format = formData.get('format') as string || 'jpeg';
    const targetSize = parseInt(formData.get('targetSize') as string) || undefined;

    if (!files || files.length === 0) {
      return NextResponse.json({ error: 'No files provided' }, { status: 400 });
    }

    const zip = new JSZip();
    const processedImages: Array<{
      name: string;
      originalSize: number;
      outputSize: number;
      originalDimensions: { width: number; height: number };
      outputDimensions: { width: number; height: number };
    }> = [];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const buffer = Buffer.from(await file.arrayBuffer());
      let sharpInstance = sharp(buffer);

      // Get original metadata
      const metadata = await sharpInstance.metadata();
      
      // Resize if dimensions provided
      if (width || height) {
        sharpInstance = sharpInstance.resize(width, height, {
          fit: 'inside',
          withoutEnlargement: true
        });
      }

      // Convert format and apply quality
      let outputBuffer: Buffer;
      switch (format.toLowerCase()) {
        case 'jpeg':
        case 'jpg':
          outputBuffer = await sharpInstance.jpeg({ quality }).toBuffer();
          break;
        case 'png':
          outputBuffer = await sharpInstance.png({ quality: Math.round(quality / 10) }).toBuffer();
          break;
        case 'webp':
          outputBuffer = await sharpInstance.webp({ quality }).toBuffer();
          break;
        case 'avif':
          outputBuffer = await sharpInstance.avif({ quality }).toBuffer();
          break;
        default:
          outputBuffer = await sharpInstance.jpeg({ quality }).toBuffer();
      }

      // If target size is specified, iteratively reduce quality
      if (targetSize) {
        const targetBytes = targetSize * 1024;
        let currentQuality = quality;
        
        while (outputBuffer.length > targetBytes && currentQuality > 10) {
          currentQuality -= 10;
          
          switch (format.toLowerCase()) {
            case 'jpeg':
            case 'jpg':
              outputBuffer = await sharp(buffer)
                .resize(width, height, { fit: 'inside', withoutEnlargement: true })
                .jpeg({ quality: currentQuality })
                .toBuffer();
              break;
            case 'png':
              outputBuffer = await sharp(buffer)
                .resize(width, height, { fit: 'inside', withoutEnlargement: true })
                .png({ quality: Math.round(currentQuality / 10) })
                .toBuffer();
              break;
            case 'webp':
              outputBuffer = await sharp(buffer)
                .resize(width, height, { fit: 'inside', withoutEnlargement: true })
                .webp({ quality: currentQuality })
                .toBuffer();
              break;
            case 'avif':
              outputBuffer = await sharp(buffer)
                .resize(width, height, { fit: 'inside', withoutEnlargement: true })
                .avif({ quality: currentQuality })
                .toBuffer();
              break;
          }
        }
      }

      const outputMetadata = await sharp(outputBuffer).metadata();
      
      // Generate filename
      const originalName = file.name.split('.')[0];
      const filename = `${originalName}_resized.${format}`;
      
      // Add to ZIP
      zip.file(filename, outputBuffer);
      
      // Track processing info
      processedImages.push({
        name: filename,
        originalSize: buffer.length,
        outputSize: outputBuffer.length,
        originalDimensions: { 
          width: metadata.width || 0, 
          height: metadata.height || 0 
        },
        outputDimensions: { 
          width: outputMetadata.width || 0, 
          height: outputMetadata.height || 0 
        }
      });
    }

    // Generate ZIP file
    const zipBuffer = await zip.generateAsync({ type: 'nodebuffer' });

    return new NextResponse(zipBuffer, {
      headers: {
        'Content-Type': 'application/zip',
        'Content-Disposition': 'attachment; filename="resized_images.zip"',
        'X-Processed-Count': processedImages.length.toString(),
        'X-Processing-Info': JSON.stringify(processedImages),
      },
    });
  } catch (error) {
    console.error('Error processing batch images:', error);
    return NextResponse.json({ error: 'Failed to process images' }, { status: 500 });
  }
}
