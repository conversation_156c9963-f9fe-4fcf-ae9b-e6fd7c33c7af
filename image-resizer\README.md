# Image Resizer - Professional Image Processing Tool

A powerful, web-based image processing application built with Next.js that allows you to resize, compress, convert, crop, and upscale images with professional quality results.

## 🚀 Features

### Core Functionality
- **Smart Image Resizing** - Resize images to exact dimensions or by percentage while maintaining aspect ratio
- **File Size Compression** - Reduce file size to specific KB/MB targets without losing visual quality
- **Format Conversion** - Convert between JPEG, PNG, WebP, and AVIF formats
- **Image Cropping** - Interactive cropping with preset aspect ratios and custom selection
- **AI Upscaling** - Enhance image resolution up to 4x using advanced interpolation algorithms

### Batch Processing
- **Multiple File Upload** - Drag & drop or select multiple images at once
- **Batch Processing** - Process multiple images with the same settings
- **ZIP Download** - Automatically package processed images into a ZIP file

### Supported Formats
- **Input**: JPEG, PNG, GIF, BMP, WebP, AVIF, TIFF
- **Output**: JPEG, PNG, WebP, AVIF

## 🛠️ Technology Stack

- **Frontend**: Next.js 15, React 18, TypeScript
- **Styling**: Tailwind CSS
- **Image Processing**: Sharp (Node.js)
- **File Handling**: JSZ<PERSON>, React Dropzone
- **UI Components**: Lucide React Icons, React Image Crop

## 📦 Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd image-resizer
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser

## 🎯 Usage

### Basic Image Resizing
1. Upload one or more images using the drag & drop interface
2. Select "Resize" operation
3. Set desired width/height or choose from preset sizes
4. Adjust quality settings (10-100%)
5. Choose output format
6. Click "Process" to download results

### File Size Compression
1. Upload images
2. Select "Compress" operation
3. Set target file size in KB
4. The system will automatically adjust quality to meet the target size
5. Download compressed images

### Format Conversion
1. Upload images in any supported format
2. Select "Convert" operation
3. Choose desired output format (JPEG, PNG, WebP, AVIF)
4. Set quality and download

### Image Cropping
1. Upload a single image
2. Select "Crop" operation
3. Use the interactive cropper to select area
4. Choose from preset aspect ratios or crop freely
5. Apply crop and download

### Image Upscaling
1. Upload images
2. Select "Upscale" operation
3. Choose scale factor (1x to 4x)
4. Process and download enhanced images

## 🔧 API Endpoints

The application provides several API endpoints for image processing:

- `POST /api/resize` - Resize single images
- `POST /api/batch-resize` - Process multiple images
- `POST /api/crop` - Crop images
- `POST /api/upscale` - Upscale images

## 🚀 Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Deploy with one click

### Docker
```bash
# Build the image
docker build -t image-resizer .

# Run the container
docker run -p 3000:3000 image-resizer
```

### Manual Deployment
```bash
# Build the application
npm run build

# Start the production server
npm start
```

## 🔒 Environment Variables

No environment variables are required for basic functionality. All image processing happens server-side using Sharp.

## 📝 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📞 Support

If you encounter any issues or have questions, please open an issue on GitHub.

---

Built with ❤️ using Next.js and Sharp
