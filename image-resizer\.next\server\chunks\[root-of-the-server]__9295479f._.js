module.exports = {

"[project]/.next-internal/server/app/api/resize/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/sharp [external] (sharp, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("sharp", () => require("sharp"));

module.exports = mod;
}}),
"[project]/src/app/api/resize/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$sharp__$5b$external$5d$__$28$sharp$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/sharp [external] (sharp, cjs)");
;
;
async function POST(request) {
    try {
        const formData = await request.formData();
        const file = formData.get('file');
        const width = parseInt(formData.get('width')) || undefined;
        const height = parseInt(formData.get('height')) || undefined;
        const quality = parseInt(formData.get('quality')) || 80;
        const format = formData.get('format') || 'jpeg';
        const targetSize = parseInt(formData.get('targetSize')) || undefined; // in KB
        if (!file) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'No file provided'
            }, {
                status: 400
            });
        }
        const buffer = Buffer.from(await file.arrayBuffer());
        let sharpInstance = (0, __TURBOPACK__imported__module__$5b$externals$5d2f$sharp__$5b$external$5d$__$28$sharp$2c$__cjs$29$__["default"])(buffer);
        // Get original metadata
        const metadata = await sharpInstance.metadata();
        // Resize if dimensions provided
        if (width || height) {
            sharpInstance = sharpInstance.resize(width, height, {
                fit: 'inside',
                withoutEnlargement: true
            });
        }
        // Convert format and apply quality
        let outputBuffer;
        switch(format.toLowerCase()){
            case 'jpeg':
            case 'jpg':
                outputBuffer = await sharpInstance.jpeg({
                    quality
                }).toBuffer();
                break;
            case 'png':
                outputBuffer = await sharpInstance.png({
                    quality: Math.round(quality / 10)
                }).toBuffer();
                break;
            case 'webp':
                outputBuffer = await sharpInstance.webp({
                    quality
                }).toBuffer();
                break;
            case 'avif':
                outputBuffer = await sharpInstance.avif({
                    quality
                }).toBuffer();
                break;
            default:
                outputBuffer = await sharpInstance.jpeg({
                    quality
                }).toBuffer();
        }
        // If target size is specified, iteratively reduce quality
        if (targetSize) {
            const targetBytes = targetSize * 1024;
            let currentQuality = quality;
            while(outputBuffer.length > targetBytes && currentQuality > 10){
                currentQuality -= 10;
                switch(format.toLowerCase()){
                    case 'jpeg':
                    case 'jpg':
                        outputBuffer = await (0, __TURBOPACK__imported__module__$5b$externals$5d2f$sharp__$5b$external$5d$__$28$sharp$2c$__cjs$29$__["default"])(buffer).resize(width, height, {
                            fit: 'inside',
                            withoutEnlargement: true
                        }).jpeg({
                            quality: currentQuality
                        }).toBuffer();
                        break;
                    case 'png':
                        outputBuffer = await (0, __TURBOPACK__imported__module__$5b$externals$5d2f$sharp__$5b$external$5d$__$28$sharp$2c$__cjs$29$__["default"])(buffer).resize(width, height, {
                            fit: 'inside',
                            withoutEnlargement: true
                        }).png({
                            quality: Math.round(currentQuality / 10)
                        }).toBuffer();
                        break;
                    case 'webp':
                        outputBuffer = await (0, __TURBOPACK__imported__module__$5b$externals$5d2f$sharp__$5b$external$5d$__$28$sharp$2c$__cjs$29$__["default"])(buffer).resize(width, height, {
                            fit: 'inside',
                            withoutEnlargement: true
                        }).webp({
                            quality: currentQuality
                        }).toBuffer();
                        break;
                    case 'avif':
                        outputBuffer = await (0, __TURBOPACK__imported__module__$5b$externals$5d2f$sharp__$5b$external$5d$__$28$sharp$2c$__cjs$29$__["default"])(buffer).resize(width, height, {
                            fit: 'inside',
                            withoutEnlargement: true
                        }).avif({
                            quality: currentQuality
                        }).toBuffer();
                        break;
                }
            }
        }
        const outputMetadata = await (0, __TURBOPACK__imported__module__$5b$externals$5d2f$sharp__$5b$external$5d$__$28$sharp$2c$__cjs$29$__["default"])(outputBuffer).metadata();
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](outputBuffer, {
            headers: {
                'Content-Type': `image/${format}`,
                'Content-Disposition': `attachment; filename="resized.${format}"`,
                'X-Original-Size': metadata.size?.toString() || '0',
                'X-Output-Size': outputBuffer.length.toString(),
                'X-Original-Width': metadata.width?.toString() || '0',
                'X-Original-Height': metadata.height?.toString() || '0',
                'X-Output-Width': outputMetadata.width?.toString() || '0',
                'X-Output-Height': outputMetadata.height?.toString() || '0'
            }
        });
    } catch (error) {
        console.error('Error processing image:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to process image'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__9295479f._.js.map