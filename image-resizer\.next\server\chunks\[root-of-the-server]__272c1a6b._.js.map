{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/NukeXDs/ImageResizer/image-resizer/src/app/api/batch-resize/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport sharp from 'sharp';\nimport <PERSON><PERSON><PERSON><PERSON> from 'jszip';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const formData = await request.formData();\n    const files = formData.getAll('files') as File[];\n    const width = parseInt(formData.get('width') as string) || undefined;\n    const height = parseInt(formData.get('height') as string) || undefined;\n    const quality = parseInt(formData.get('quality') as string) || 80;\n    const format = formData.get('format') as string || 'jpeg';\n    const targetSize = parseInt(formData.get('targetSize') as string) || undefined;\n\n    if (!files || files.length === 0) {\n      return NextResponse.json({ error: 'No files provided' }, { status: 400 });\n    }\n\n    if (files.length > 50) {\n      return NextResponse.json({ error: 'Maximum 50 files allowed' }, { status: 400 });\n    }\n\n    const zip = new JSZip();\n    const processedImages: Array<{\n      name: string;\n      originalSize: number;\n      outputSize: number;\n      originalDimensions: { width: number; height: number };\n      outputDimensions: { width: number; height: number };\n    }> = [];\n\n    for (let i = 0; i < files.length; i++) {\n      const file = files[i];\n      const buffer = Buffer.from(await file.arrayBuffer());\n      let sharpInstance = sharp(buffer);\n\n      // Get original metadata\n      const metadata = await sharpInstance.metadata();\n      \n      // Resize if dimensions provided\n      if (width || height) {\n        sharpInstance = sharpInstance.resize(width, height, {\n          fit: 'inside',\n          withoutEnlargement: true\n        });\n      }\n\n      // Convert format and apply quality\n      let outputBuffer: Buffer;\n      switch (format.toLowerCase()) {\n        case 'jpeg':\n        case 'jpg':\n          outputBuffer = await sharpInstance.jpeg({ quality }).toBuffer();\n          break;\n        case 'png':\n          outputBuffer = await sharpInstance.png({ quality: Math.round(quality / 10) }).toBuffer();\n          break;\n        case 'webp':\n          outputBuffer = await sharpInstance.webp({ quality }).toBuffer();\n          break;\n        case 'avif':\n          outputBuffer = await sharpInstance.avif({ quality }).toBuffer();\n          break;\n        default:\n          outputBuffer = await sharpInstance.jpeg({ quality }).toBuffer();\n      }\n\n      // If target size is specified, iteratively reduce quality\n      if (targetSize) {\n        const targetBytes = targetSize * 1024;\n        let currentQuality = quality;\n        \n        while (outputBuffer.length > targetBytes && currentQuality > 10) {\n          currentQuality -= 10;\n          \n          switch (format.toLowerCase()) {\n            case 'jpeg':\n            case 'jpg':\n              outputBuffer = await sharp(buffer)\n                .resize(width, height, { fit: 'inside', withoutEnlargement: true })\n                .jpeg({ quality: currentQuality })\n                .toBuffer();\n              break;\n            case 'png':\n              outputBuffer = await sharp(buffer)\n                .resize(width, height, { fit: 'inside', withoutEnlargement: true })\n                .png({ quality: Math.round(currentQuality / 10) })\n                .toBuffer();\n              break;\n            case 'webp':\n              outputBuffer = await sharp(buffer)\n                .resize(width, height, { fit: 'inside', withoutEnlargement: true })\n                .webp({ quality: currentQuality })\n                .toBuffer();\n              break;\n            case 'avif':\n              outputBuffer = await sharp(buffer)\n                .resize(width, height, { fit: 'inside', withoutEnlargement: true })\n                .avif({ quality: currentQuality })\n                .toBuffer();\n              break;\n          }\n        }\n      }\n\n      const outputMetadata = await sharp(outputBuffer).metadata();\n      \n      // Generate filename\n      const originalName = file.name.split('.')[0];\n      const filename = `${originalName}_resized.${format}`;\n      \n      // Add to ZIP\n      zip.file(filename, outputBuffer);\n      \n      // Track processing info\n      processedImages.push({\n        name: filename,\n        originalSize: buffer.length,\n        outputSize: outputBuffer.length,\n        originalDimensions: { \n          width: metadata.width || 0, \n          height: metadata.height || 0 \n        },\n        outputDimensions: { \n          width: outputMetadata.width || 0, \n          height: outputMetadata.height || 0 \n        }\n      });\n    }\n\n    // Generate ZIP file\n    const zipBuffer = await zip.generateAsync({ type: 'nodebuffer' });\n\n    return new NextResponse(zipBuffer, {\n      headers: {\n        'Content-Type': 'application/zip',\n        'Content-Disposition': 'attachment; filename=\"resized_images.zip\"',\n        'X-Processed-Count': processedImages.length.toString(),\n        'X-Processing-Info': JSON.stringify(processedImages),\n      },\n    });\n  } catch (error) {\n    console.error('Error processing batch images:', error);\n    return NextResponse.json({ error: 'Failed to process images' }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,QAAQ,QAAQ;QACvC,MAAM,QAAQ,SAAS,MAAM,CAAC;QAC9B,MAAM,QAAQ,SAAS,SAAS,GAAG,CAAC,aAAuB;QAC3D,MAAM,SAAS,SAAS,SAAS,GAAG,CAAC,cAAwB;QAC7D,MAAM,UAAU,SAAS,SAAS,GAAG,CAAC,eAAyB;QAC/D,MAAM,SAAS,SAAS,GAAG,CAAC,aAAuB;QACnD,MAAM,aAAa,SAAS,SAAS,GAAG,CAAC,kBAA4B;QAErE,IAAI,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;YAChC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAoB,GAAG;gBAAE,QAAQ;YAAI;QACzE;QAEA,IAAI,MAAM,MAAM,GAAG,IAAI;YACrB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA2B,GAAG;gBAAE,QAAQ;YAAI;QAChF;QAEA,MAAM,MAAM,IAAI,uIAAA,CAAA,UAAK;QACrB,MAAM,kBAMD,EAAE;QAEP,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACrC,MAAM,OAAO,KAAK,CAAC,EAAE;YACrB,MAAM,SAAS,OAAO,IAAI,CAAC,MAAM,KAAK,WAAW;YACjD,IAAI,gBAAgB,CAAA,GAAA,mGAAA,CAAA,UAAK,AAAD,EAAE;YAE1B,wBAAwB;YACxB,MAAM,WAAW,MAAM,cAAc,QAAQ;YAE7C,gCAAgC;YAChC,IAAI,SAAS,QAAQ;gBACnB,gBAAgB,cAAc,MAAM,CAAC,OAAO,QAAQ;oBAClD,KAAK;oBACL,oBAAoB;gBACtB;YACF;YAEA,mCAAmC;YACnC,IAAI;YACJ,OAAQ,OAAO,WAAW;gBACxB,KAAK;gBACL,KAAK;oBACH,eAAe,MAAM,cAAc,IAAI,CAAC;wBAAE;oBAAQ,GAAG,QAAQ;oBAC7D;gBACF,KAAK;oBACH,eAAe,MAAM,cAAc,GAAG,CAAC;wBAAE,SAAS,KAAK,KAAK,CAAC,UAAU;oBAAI,GAAG,QAAQ;oBACtF;gBACF,KAAK;oBACH,eAAe,MAAM,cAAc,IAAI,CAAC;wBAAE;oBAAQ,GAAG,QAAQ;oBAC7D;gBACF,KAAK;oBACH,eAAe,MAAM,cAAc,IAAI,CAAC;wBAAE;oBAAQ,GAAG,QAAQ;oBAC7D;gBACF;oBACE,eAAe,MAAM,cAAc,IAAI,CAAC;wBAAE;oBAAQ,GAAG,QAAQ;YACjE;YAEA,0DAA0D;YAC1D,IAAI,YAAY;gBACd,MAAM,cAAc,aAAa;gBACjC,IAAI,iBAAiB;gBAErB,MAAO,aAAa,MAAM,GAAG,eAAe,iBAAiB,GAAI;oBAC/D,kBAAkB;oBAElB,OAAQ,OAAO,WAAW;wBACxB,KAAK;wBACL,KAAK;4BACH,eAAe,MAAM,CAAA,GAAA,mGAAA,CAAA,UAAK,AAAD,EAAE,QACxB,MAAM,CAAC,OAAO,QAAQ;gCAAE,KAAK;gCAAU,oBAAoB;4BAAK,GAChE,IAAI,CAAC;gCAAE,SAAS;4BAAe,GAC/B,QAAQ;4BACX;wBACF,KAAK;4BACH,eAAe,MAAM,CAAA,GAAA,mGAAA,CAAA,UAAK,AAAD,EAAE,QACxB,MAAM,CAAC,OAAO,QAAQ;gCAAE,KAAK;gCAAU,oBAAoB;4BAAK,GAChE,GAAG,CAAC;gCAAE,SAAS,KAAK,KAAK,CAAC,iBAAiB;4BAAI,GAC/C,QAAQ;4BACX;wBACF,KAAK;4BACH,eAAe,MAAM,CAAA,GAAA,mGAAA,CAAA,UAAK,AAAD,EAAE,QACxB,MAAM,CAAC,OAAO,QAAQ;gCAAE,KAAK;gCAAU,oBAAoB;4BAAK,GAChE,IAAI,CAAC;gCAAE,SAAS;4BAAe,GAC/B,QAAQ;4BACX;wBACF,KAAK;4BACH,eAAe,MAAM,CAAA,GAAA,mGAAA,CAAA,UAAK,AAAD,EAAE,QACxB,MAAM,CAAC,OAAO,QAAQ;gCAAE,KAAK;gCAAU,oBAAoB;4BAAK,GAChE,IAAI,CAAC;gCAAE,SAAS;4BAAe,GAC/B,QAAQ;4BACX;oBACJ;gBACF;YACF;YAEA,MAAM,iBAAiB,MAAM,CAAA,GAAA,mGAAA,CAAA,UAAK,AAAD,EAAE,cAAc,QAAQ;YAEzD,oBAAoB;YACpB,MAAM,eAAe,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;YAC5C,MAAM,WAAW,GAAG,aAAa,SAAS,EAAE,QAAQ;YAEpD,aAAa;YACb,IAAI,IAAI,CAAC,UAAU;YAEnB,wBAAwB;YACxB,gBAAgB,IAAI,CAAC;gBACnB,MAAM;gBACN,cAAc,OAAO,MAAM;gBAC3B,YAAY,aAAa,MAAM;gBAC/B,oBAAoB;oBAClB,OAAO,SAAS,KAAK,IAAI;oBACzB,QAAQ,SAAS,MAAM,IAAI;gBAC7B;gBACA,kBAAkB;oBAChB,OAAO,eAAe,KAAK,IAAI;oBAC/B,QAAQ,eAAe,MAAM,IAAI;gBACnC;YACF;QACF;QAEA,oBAAoB;QACpB,MAAM,YAAY,MAAM,IAAI,aAAa,CAAC;YAAE,MAAM;QAAa;QAE/D,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,WAAW;YACjC,SAAS;gBACP,gBAAgB;gBAChB,uBAAuB;gBACvB,qBAAqB,gBAAgB,MAAM,CAAC,QAAQ;gBACpD,qBAAqB,KAAK,SAAS,CAAC;YACtC;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAA2B,GAAG;YAAE,QAAQ;QAAI;IAChF;AACF", "debugId": null}}]}