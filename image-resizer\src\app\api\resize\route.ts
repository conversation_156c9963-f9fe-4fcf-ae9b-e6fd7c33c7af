import { NextRequest, NextResponse } from 'next/server';
import sharp from 'sharp';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const width = parseInt(formData.get('width') as string) || undefined;
    const height = parseInt(formData.get('height') as string) || undefined;
    const quality = parseInt(formData.get('quality') as string) || 80;
    const format = formData.get('format') as string || 'jpeg';
    const targetSize = parseInt(formData.get('targetSize') as string) || undefined; // in KB

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    const buffer = Buffer.from(await file.arrayBuffer());
    let sharpInstance = sharp(buffer);

    // Get original metadata
    const metadata = await sharpInstance.metadata();
    
    // Resize if dimensions provided
    if (width || height) {
      sharpInstance = sharpInstance.resize(width, height, {
        fit: 'inside',
        withoutEnlargement: true
      });
    }

    // Convert format and apply quality
    let outputBuffer: Buffer;
    switch (format.toLowerCase()) {
      case 'jpeg':
      case 'jpg':
        outputBuffer = await sharpInstance.jpeg({ quality }).toBuffer();
        break;
      case 'png':
        outputBuffer = await sharpInstance.png({ quality: Math.round(quality / 10) }).toBuffer();
        break;
      case 'webp':
        outputBuffer = await sharpInstance.webp({ quality }).toBuffer();
        break;
      case 'avif':
        outputBuffer = await sharpInstance.avif({ quality }).toBuffer();
        break;
      default:
        outputBuffer = await sharpInstance.jpeg({ quality }).toBuffer();
    }

    // If target size is specified, iteratively reduce quality
    if (targetSize) {
      const targetBytes = targetSize * 1024;
      let currentQuality = quality;
      
      while (outputBuffer.length > targetBytes && currentQuality > 10) {
        currentQuality -= 10;
        
        switch (format.toLowerCase()) {
          case 'jpeg':
          case 'jpg':
            outputBuffer = await sharp(buffer)
              .resize(width, height, { fit: 'inside', withoutEnlargement: true })
              .jpeg({ quality: currentQuality })
              .toBuffer();
            break;
          case 'png':
            outputBuffer = await sharp(buffer)
              .resize(width, height, { fit: 'inside', withoutEnlargement: true })
              .png({ quality: Math.round(currentQuality / 10) })
              .toBuffer();
            break;
          case 'webp':
            outputBuffer = await sharp(buffer)
              .resize(width, height, { fit: 'inside', withoutEnlargement: true })
              .webp({ quality: currentQuality })
              .toBuffer();
            break;
          case 'avif':
            outputBuffer = await sharp(buffer)
              .resize(width, height, { fit: 'inside', withoutEnlargement: true })
              .avif({ quality: currentQuality })
              .toBuffer();
            break;
        }
      }
    }

    const outputMetadata = await sharp(outputBuffer).metadata();

    return new NextResponse(outputBuffer, {
      headers: {
        'Content-Type': `image/${format}`,
        'Content-Disposition': `attachment; filename="resized.${format}"`,
        'X-Original-Size': metadata.size?.toString() || '0',
        'X-Output-Size': outputBuffer.length.toString(),
        'X-Original-Width': metadata.width?.toString() || '0',
        'X-Original-Height': metadata.height?.toString() || '0',
        'X-Output-Width': outputMetadata.width?.toString() || '0',
        'X-Output-Height': outputMetadata.height?.toString() || '0',
      },
    });
  } catch (error) {
    console.error('Error processing image:', error);
    return NextResponse.json({ error: 'Failed to process image' }, { status: 500 });
  }
}
