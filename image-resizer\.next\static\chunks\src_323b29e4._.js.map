{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/NukeXDs/ImageResizer/image-resizer/src/components/FileUpload.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useCallback, useState } from 'react';\nimport { useDropzone } from 'react-dropzone';\nimport { Upload, X, Image as ImageIcon } from 'lucide-react';\n\ninterface FileUploadProps {\n  onFilesSelected: (files: File[]) => void;\n  multiple?: boolean;\n  maxFiles?: number;\n}\n\nexport default function FileUpload({ onFilesSelected, multiple = true, maxFiles = 10 }: FileUploadProps) {\n  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);\n\n  const onDrop = useCallback((acceptedFiles: File[]) => {\n    const imageFiles = acceptedFiles.filter(file => file.type.startsWith('image/'));\n    setSelectedFiles(prev => {\n      const newFiles = multiple ? [...prev, ...imageFiles].slice(0, maxFiles) : imageFiles.slice(0, 1);\n      onFilesSelected(newFiles);\n      return newFiles;\n    });\n  }, [onFilesSelected, multiple, maxFiles]);\n\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\n    onDrop,\n    accept: {\n      'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.bmp', '.webp', '.avif', '.tiff']\n    },\n    multiple,\n    maxFiles\n  });\n\n  const removeFile = (index: number) => {\n    setSelectedFiles(prev => {\n      const newFiles = prev.filter((_, i) => i !== index);\n      onFilesSelected(newFiles);\n      return newFiles;\n    });\n  };\n\n  const formatFileSize = (bytes: number) => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  return (\n    <div className=\"w-full\">\n      <div\n        {...getRootProps()}\n        className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${\n          isDragActive\n            ? 'border-blue-500 bg-blue-50'\n            : 'border-gray-300 hover:border-gray-400'\n        }`}\n      >\n        <input {...getInputProps()} />\n        <Upload className=\"mx-auto h-12 w-12 text-gray-400 mb-4\" />\n        {isDragActive ? (\n          <p className=\"text-blue-600\">Drop the images here...</p>\n        ) : (\n          <div>\n            <p className=\"text-gray-600 mb-2\">\n              Drag & drop images here, or click to select\n            </p>\n            <p className=\"text-sm text-gray-500\">\n              Supports: JPEG, PNG, GIF, BMP, WebP, AVIF, TIFF\n            </p>\n            {multiple && (\n              <p className=\"text-sm text-gray-500\">\n                Maximum {maxFiles} files\n              </p>\n            )}\n          </div>\n        )}\n      </div>\n\n      {selectedFiles.length > 0 && (\n        <div className=\"mt-6\">\n          <h3 className=\"text-lg font-semibold mb-3\">Selected Files ({selectedFiles.length})</h3>\n          <div className=\"space-y-2\">\n            {selectedFiles.map((file, index) => (\n              <div\n                key={index}\n                className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\"\n              >\n                <div className=\"flex items-center space-x-3\">\n                  <ImageIcon className=\"h-5 w-5 text-gray-500\" />\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-900\">{file.name}</p>\n                    <p className=\"text-xs text-gray-500\">{formatFileSize(file.size)}</p>\n                  </div>\n                </div>\n                <button\n                  onClick={() => removeFile(index)}\n                  className=\"p-1 hover:bg-gray-200 rounded-full transition-colors\"\n                >\n                  <X className=\"h-4 w-4 text-gray-500\" />\n                </button>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;;;AAJA;;;;AAYe,SAAS,WAAW,EAAE,eAAe,EAAE,WAAW,IAAI,EAAE,WAAW,EAAE,EAAmB;;IACrG,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAE7D,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0CAAE,CAAC;YAC1B,MAAM,aAAa,cAAc,MAAM;6DAAC,CAAA,OAAQ,KAAK,IAAI,CAAC,UAAU,CAAC;;YACrE;kDAAiB,CAAA;oBACf,MAAM,WAAW,WAAW;2BAAI;2BAAS;qBAAW,CAAC,KAAK,CAAC,GAAG,YAAY,WAAW,KAAK,CAAC,GAAG;oBAC9F,gBAAgB;oBAChB,OAAO;gBACT;;QACF;yCAAG;QAAC;QAAiB;QAAU;KAAS;IAExC,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE;QAChE;QACA,QAAQ;YACN,WAAW;gBAAC;gBAAS;gBAAQ;gBAAQ;gBAAQ;gBAAQ;gBAAS;gBAAS;aAAQ;QACjF;QACA;QACA;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,iBAAiB,CAAA;YACf,MAAM,WAAW,KAAK,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;YAC7C,gBAAgB;YAChB,OAAO;QACT;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACE,GAAG,cAAc;gBAClB,WAAW,CAAC,mFAAmF,EAC7F,eACI,+BACA,yCACJ;;kCAEF,6LAAC;wBAAO,GAAG,eAAe;;;;;;kCAC1B,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;oBACjB,6BACC,6LAAC;wBAAE,WAAU;kCAAgB;;;;;6CAE7B,6LAAC;;0CACC,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAGlC,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;4BAGpC,0BACC,6LAAC;gCAAE,WAAU;;oCAAwB;oCAC1B;oCAAS;;;;;;;;;;;;;;;;;;;YAO3B,cAAc,MAAM,GAAG,mBACtB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;4BAA6B;4BAAiB,cAAc,MAAM;4BAAC;;;;;;;kCACjF,6LAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAC,MAAM,sBACxB,6LAAC;gCAEC,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAS;gDAAC,WAAU;;;;;;0DACrB,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAqC,KAAK,IAAI;;;;;;kEAC3D,6LAAC;wDAAE,WAAU;kEAAyB,eAAe,KAAK,IAAI;;;;;;;;;;;;;;;;;;kDAGlE,6LAAC;wCACC,SAAS,IAAM,WAAW;wCAC1B,WAAU;kDAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;+BAdV;;;;;;;;;;;;;;;;;;;;;;AAuBrB;GAjGwB;;QAYgC,2KAAA,CAAA,cAAW;;;KAZ3C", "debugId": null}}, {"offset": {"line": 269, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/NukeXDs/ImageResizer/image-resizer/src/components/ImageControls.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { Settings, Download, Loader2 } from 'lucide-react';\n\ninterface ImageControlsProps {\n  files: File[];\n  onProcess: (options: ProcessingOptions) => void;\n  isProcessing: boolean;\n}\n\nexport interface ProcessingOptions {\n  width?: number;\n  height?: number;\n  quality: number;\n  format: string;\n  targetSize?: number;\n  operation: 'resize' | 'compress' | 'convert' | 'crop' | 'upscale';\n  cropData?: {\n    x: number;\n    y: number;\n    width: number;\n    height: number;\n  };\n  scale?: number;\n}\n\nexport default function ImageControls({ files, onProcess, isProcessing }: ImageControlsProps) {\n  const [operation, setOperation] = useState<ProcessingOptions['operation']>('resize');\n  const [width, setWidth] = useState<string>('');\n  const [height, setHeight] = useState<string>('');\n  const [quality, setQuality] = useState(80);\n  const [format, setFormat] = useState('jpeg');\n  const [targetSize, setTargetSize] = useState<string>('');\n  const [scale, setScale] = useState(2);\n  const [maintainAspectRatio, setMaintainAspectRatio] = useState(true);\n\n  const handleProcess = () => {\n    const options: ProcessingOptions = {\n      quality,\n      format,\n      operation,\n    };\n\n    if (width) options.width = parseInt(width);\n    if (height) options.height = parseInt(height);\n    if (targetSize) options.targetSize = parseInt(targetSize);\n    if (operation === 'upscale') options.scale = scale;\n\n    onProcess(options);\n  };\n\n  const presetSizes = [\n    { name: 'Instagram Square', width: 1080, height: 1080 },\n    { name: 'Instagram Story', width: 1080, height: 1920 },\n    { name: 'Facebook Cover', width: 1200, height: 630 },\n    { name: 'Twitter Header', width: 1500, height: 500 },\n    { name: 'YouTube Thumbnail', width: 1280, height: 720 },\n    { name: 'HD', width: 1920, height: 1080 },\n    { name: '4K', width: 3840, height: 2160 },\n  ];\n\n  const applyPreset = (preset: { width: number; height: number }) => {\n    setWidth(preset.width.toString());\n    setHeight(preset.height.toString());\n  };\n\n  return (\n    <div className=\"bg-white p-6 rounded-lg shadow-lg\">\n      <div className=\"flex items-center mb-6\">\n        <Settings className=\"h-5 w-5 mr-2\" />\n        <h2 className=\"text-xl font-semibold\">Processing Options</h2>\n      </div>\n\n      {/* Operation Selection */}\n      <div className=\"mb-6\">\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Operation\n        </label>\n        <div className=\"grid grid-cols-2 md:grid-cols-5 gap-2\">\n          {[\n            { value: 'resize', label: 'Resize' },\n            { value: 'compress', label: 'Compress' },\n            { value: 'convert', label: 'Convert' },\n            { value: 'crop', label: 'Crop' },\n            { value: 'upscale', label: 'Upscale' },\n          ].map((op) => (\n            <button\n              key={op.value}\n              onClick={() => setOperation(op.value as ProcessingOptions['operation'])}\n              className={`p-2 text-sm rounded-md border ${\n                operation === op.value\n                  ? 'bg-blue-500 text-white border-blue-500'\n                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'\n              }`}\n            >\n              {op.label}\n            </button>\n          ))}\n        </div>\n      </div>\n\n      {/* Dimensions */}\n      {(operation === 'resize' || operation === 'crop') && (\n        <div className=\"mb-6\">\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Dimensions\n          </label>\n          <div className=\"grid grid-cols-2 gap-4 mb-3\">\n            <div>\n              <label className=\"block text-xs text-gray-500 mb-1\">Width (px)</label>\n              <input\n                type=\"number\"\n                value={width}\n                onChange={(e) => setWidth(e.target.value)}\n                className=\"w-full p-2 border border-gray-300 rounded-md\"\n                placeholder=\"Auto\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-xs text-gray-500 mb-1\">Height (px)</label>\n              <input\n                type=\"number\"\n                value={height}\n                onChange={(e) => setHeight(e.target.value)}\n                className=\"w-full p-2 border border-gray-300 rounded-md\"\n                placeholder=\"Auto\"\n              />\n            </div>\n          </div>\n          \n          {operation === 'resize' && (\n            <>\n              <div className=\"mb-3\">\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={maintainAspectRatio}\n                    onChange={(e) => setMaintainAspectRatio(e.target.checked)}\n                    className=\"mr-2\"\n                  />\n                  <span className=\"text-sm text-gray-700\">Maintain aspect ratio</span>\n                </label>\n              </div>\n              \n              <div>\n                <label className=\"block text-xs text-gray-500 mb-2\">Quick Presets</label>\n                <div className=\"grid grid-cols-2 md:grid-cols-3 gap-2\">\n                  {presetSizes.map((preset) => (\n                    <button\n                      key={preset.name}\n                      onClick={() => applyPreset(preset)}\n                      className=\"p-2 text-xs bg-gray-100 hover:bg-gray-200 rounded-md transition-colors\"\n                    >\n                      {preset.name}\n                      <br />\n                      <span className=\"text-gray-500\">{preset.width}×{preset.height}</span>\n                    </button>\n                  ))}\n                </div>\n              </div>\n            </>\n          )}\n        </div>\n      )}\n\n      {/* Upscale Factor */}\n      {operation === 'upscale' && (\n        <div className=\"mb-6\">\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Scale Factor: {scale}x\n          </label>\n          <input\n            type=\"range\"\n            min=\"1\"\n            max=\"4\"\n            step=\"0.1\"\n            value={scale}\n            onChange={(e) => setScale(parseFloat(e.target.value))}\n            className=\"w-full\"\n          />\n          <div className=\"flex justify-between text-xs text-gray-500 mt-1\">\n            <span>1x</span>\n            <span>2x</span>\n            <span>3x</span>\n            <span>4x</span>\n          </div>\n        </div>\n      )}\n\n      {/* Format Selection */}\n      <div className=\"mb-6\">\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Output Format\n        </label>\n        <select\n          value={format}\n          onChange={(e) => setFormat(e.target.value)}\n          className=\"w-full p-2 border border-gray-300 rounded-md\"\n        >\n          <option value=\"jpeg\">JPEG</option>\n          <option value=\"png\">PNG</option>\n          <option value=\"webp\">WebP</option>\n          <option value=\"avif\">AVIF</option>\n        </select>\n      </div>\n\n      {/* Quality */}\n      <div className=\"mb-6\">\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Quality: {quality}%\n        </label>\n        <input\n          type=\"range\"\n          min=\"10\"\n          max=\"100\"\n          value={quality}\n          onChange={(e) => setQuality(parseInt(e.target.value))}\n          className=\"w-full\"\n        />\n        <div className=\"flex justify-between text-xs text-gray-500 mt-1\">\n          <span>Low</span>\n          <span>Medium</span>\n          <span>High</span>\n        </div>\n      </div>\n\n      {/* Target File Size */}\n      <div className=\"mb-6\">\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Target File Size (KB) - Optional\n        </label>\n        <input\n          type=\"number\"\n          value={targetSize}\n          onChange={(e) => setTargetSize(e.target.value)}\n          className=\"w-full p-2 border border-gray-300 rounded-md\"\n          placeholder=\"e.g., 500\"\n        />\n        <p className=\"text-xs text-gray-500 mt-1\">\n          Leave empty to use quality setting only\n        </p>\n      </div>\n\n      {/* Process Button */}\n      <button\n        onClick={handleProcess}\n        disabled={files.length === 0 || isProcessing}\n        className=\"w-full bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 text-white font-medium py-3 px-4 rounded-md transition-colors flex items-center justify-center\"\n      >\n        {isProcessing ? (\n          <>\n            <Loader2 className=\"animate-spin h-4 w-4 mr-2\" />\n            Processing...\n          </>\n        ) : (\n          <>\n            <Download className=\"h-4 w-4 mr-2\" />\n            Process {files.length > 1 ? `${files.length} Images` : 'Image'}\n          </>\n        )}\n      </button>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;;;AAHA;;;AA2Be,SAAS,cAAc,EAAE,KAAK,EAAE,SAAS,EAAE,YAAY,EAAsB;;IAC1F,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkC;IAC3E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACrD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,MAAM,gBAAgB;QACpB,MAAM,UAA6B;YACjC;YACA;YACA;QACF;QAEA,IAAI,OAAO,QAAQ,KAAK,GAAG,SAAS;QACpC,IAAI,QAAQ,QAAQ,MAAM,GAAG,SAAS;QACtC,IAAI,YAAY,QAAQ,UAAU,GAAG,SAAS;QAC9C,IAAI,cAAc,WAAW,QAAQ,KAAK,GAAG;QAE7C,UAAU;IACZ;IAEA,MAAM,cAAc;QAClB;YAAE,MAAM;YAAoB,OAAO;YAAM,QAAQ;QAAK;QACtD;YAAE,MAAM;YAAmB,OAAO;YAAM,QAAQ;QAAK;QACrD;YAAE,MAAM;YAAkB,OAAO;YAAM,QAAQ;QAAI;QACnD;YAAE,MAAM;YAAkB,OAAO;YAAM,QAAQ;QAAI;QACnD;YAAE,MAAM;YAAqB,OAAO;YAAM,QAAQ;QAAI;QACtD;YAAE,MAAM;YAAM,OAAO;YAAM,QAAQ;QAAK;QACxC;YAAE,MAAM;YAAM,OAAO;YAAM,QAAQ;QAAK;KACzC;IAED,MAAM,cAAc,CAAC;QACnB,SAAS,OAAO,KAAK,CAAC,QAAQ;QAC9B,UAAU,OAAO,MAAM,CAAC,QAAQ;IAClC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,6LAAC;wBAAG,WAAU;kCAAwB;;;;;;;;;;;;0BAIxC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,6LAAC;wBAAI,WAAU;kCACZ;4BACC;gCAAE,OAAO;gCAAU,OAAO;4BAAS;4BACnC;gCAAE,OAAO;gCAAY,OAAO;4BAAW;4BACvC;gCAAE,OAAO;gCAAW,OAAO;4BAAU;4BACrC;gCAAE,OAAO;gCAAQ,OAAO;4BAAO;4BAC/B;gCAAE,OAAO;gCAAW,OAAO;4BAAU;yBACtC,CAAC,GAAG,CAAC,CAAC,mBACL,6LAAC;gCAEC,SAAS,IAAM,aAAa,GAAG,KAAK;gCACpC,WAAW,CAAC,8BAA8B,EACxC,cAAc,GAAG,KAAK,GAClB,2CACA,2DACJ;0CAED,GAAG,KAAK;+BARJ,GAAG,KAAK;;;;;;;;;;;;;;;;YAepB,CAAC,cAAc,YAAY,cAAc,MAAM,mBAC9C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAmC;;;;;;kDACpD,6LAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wCACxC,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAGhB,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAmC;;;;;;kDACpD,6LAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;wCACzC,WAAU;wCACV,aAAY;;;;;;;;;;;;;;;;;;oBAKjB,cAAc,0BACb;;0CACE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAM,WAAU;;sDACf,6LAAC;4CACC,MAAK;4CACL,SAAS;4CACT,UAAU,CAAC,IAAM,uBAAuB,EAAE,MAAM,CAAC,OAAO;4CACxD,WAAU;;;;;;sDAEZ,6LAAC;4CAAK,WAAU;sDAAwB;;;;;;;;;;;;;;;;;0CAI5C,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAmC;;;;;;kDACpD,6LAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC,uBAChB,6LAAC;gDAEC,SAAS,IAAM,YAAY;gDAC3B,WAAU;;oDAET,OAAO,IAAI;kEACZ,6LAAC;;;;;kEACD,6LAAC;wDAAK,WAAU;;4DAAiB,OAAO,KAAK;4DAAC;4DAAE,OAAO,MAAM;;;;;;;;+CANxD,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;;;;;YAiB/B,cAAc,2BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAM,WAAU;;4BAA+C;4BAC/C;4BAAM;;;;;;;kCAEvB,6LAAC;wBACC,MAAK;wBACL,KAAI;wBACJ,KAAI;wBACJ,MAAK;wBACL,OAAO;wBACP,UAAU,CAAC,IAAM,SAAS,WAAW,EAAE,MAAM,CAAC,KAAK;wBACnD,WAAU;;;;;;kCAEZ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAK;;;;;;0CACN,6LAAC;0CAAK;;;;;;0CACN,6LAAC;0CAAK;;;;;;0CACN,6LAAC;0CAAK;;;;;;;;;;;;;;;;;;0BAMZ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,6LAAC;wBACC,OAAO;wBACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;wBACzC,WAAU;;0CAEV,6LAAC;gCAAO,OAAM;0CAAO;;;;;;0CACrB,6LAAC;gCAAO,OAAM;0CAAM;;;;;;0CACpB,6LAAC;gCAAO,OAAM;0CAAO;;;;;;0CACrB,6LAAC;gCAAO,OAAM;0CAAO;;;;;;;;;;;;;;;;;;0BAKzB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAM,WAAU;;4BAA+C;4BACpD;4BAAQ;;;;;;;kCAEpB,6LAAC;wBACC,MAAK;wBACL,KAAI;wBACJ,KAAI;wBACJ,OAAO;wBACP,UAAU,CAAC,IAAM,WAAW,SAAS,EAAE,MAAM,CAAC,KAAK;wBACnD,WAAU;;;;;;kCAEZ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAK;;;;;;0CACN,6LAAC;0CAAK;;;;;;0CACN,6LAAC;0CAAK;;;;;;;;;;;;;;;;;;0BAKV,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,6LAAC;wBACC,MAAK;wBACL,OAAO;wBACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wBAC7C,WAAU;wBACV,aAAY;;;;;;kCAEd,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;0BAM5C,6LAAC;gBACC,SAAS;gBACT,UAAU,MAAM,MAAM,KAAK,KAAK;gBAChC,WAAU;0BAET,6BACC;;sCACE,6LAAC,oNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;wBAA8B;;iDAInD;;sCACE,6LAAC,6MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAAiB;wBAC5B,MAAM,MAAM,GAAG,IAAI,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,GAAG;;;;;;;;;;;;;;AAMnE;GA7OwB;KAAA", "debugId": null}}, {"offset": {"line": 878, "column": 0}, "map": {"version": 3, "sources": ["file:///F:/NukeXDs/ImageResizer/image-resizer/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport FileUpload from '@/components/FileUpload';\nimport ImageControls, { ProcessingOptions } from '@/components/ImageControls';\nimport { Image as ImageIcon, Zap, Scissors, ArrowUpDown, Maximize } from 'lucide-react';\n\nexport default function Home() {\n  const [files, setFiles] = useState<File[]>([]);\n  const [isProcessing, setIsProcessing] = useState(false);\n\n  const handleFilesSelected = (selectedFiles: File[]) => {\n    setFiles(selectedFiles);\n  };\n\n  const handleProcess = async (options: ProcessingOptions) => {\n    if (files.length === 0) return;\n\n    setIsProcessing(true);\n\n    try {\n      if (files.length === 1) {\n        // Single file processing\n        await processSingleFile(files[0], options);\n      } else {\n        // Batch processing\n        await processBatchFiles(files, options);\n      }\n    } catch (error) {\n      console.error('Processing error:', error);\n      alert('Error processing images. Please try again.');\n    } finally {\n      setIsProcessing(false);\n    }\n  };\n\n  const processSingleFile = async (file: File, options: ProcessingOptions) => {\n    const formData = new FormData();\n    formData.append('file', file);\n    formData.append('quality', options.quality.toString());\n    formData.append('format', options.format);\n\n    if (options.width) formData.append('width', options.width.toString());\n    if (options.height) formData.append('height', options.height.toString());\n    if (options.targetSize) formData.append('targetSize', options.targetSize.toString());\n\n    let endpoint = '/api/resize';\n\n    switch (options.operation) {\n      case 'crop':\n        endpoint = '/api/crop';\n        if (options.cropData) {\n          formData.append('x', options.cropData.x.toString());\n          formData.append('y', options.cropData.y.toString());\n          formData.append('width', options.cropData.width.toString());\n          formData.append('height', options.cropData.height.toString());\n        }\n        break;\n      case 'upscale':\n        endpoint = '/api/upscale';\n        if (options.scale) formData.append('scale', options.scale.toString());\n        break;\n      default:\n        endpoint = '/api/resize';\n    }\n\n    const response = await fetch(endpoint, {\n      method: 'POST',\n      body: formData,\n    });\n\n    if (response.ok) {\n      const blob = await response.blob();\n      const url = URL.createObjectURL(blob);\n      const a = document.createElement('a');\n      a.href = url;\n      a.download = `processed.${options.format}`;\n      document.body.appendChild(a);\n      a.click();\n      document.body.removeChild(a);\n      URL.revokeObjectURL(url);\n    } else {\n      throw new Error('Failed to process image');\n    }\n  };\n\n  const processBatchFiles = async (files: File[], options: ProcessingOptions) => {\n    const formData = new FormData();\n\n    files.forEach(file => {\n      formData.append('files', file);\n    });\n\n    formData.append('quality', options.quality.toString());\n    formData.append('format', options.format);\n\n    if (options.width) formData.append('width', options.width.toString());\n    if (options.height) formData.append('height', options.height.toString());\n    if (options.targetSize) formData.append('targetSize', options.targetSize.toString());\n\n    const response = await fetch('/api/batch-resize', {\n      method: 'POST',\n      body: formData,\n    });\n\n    if (response.ok) {\n      const blob = await response.blob();\n      const url = URL.createObjectURL(blob);\n      const a = document.createElement('a');\n      a.href = url;\n      a.download = 'processed_images.zip';\n      document.body.appendChild(a);\n      a.click();\n      document.body.removeChild(a);\n      URL.revokeObjectURL(url);\n    } else {\n      throw new Error('Failed to process images');\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n          <div className=\"flex items-center\">\n            <ImageIcon className=\"h-8 w-8 text-blue-500 mr-3\" />\n            <div>\n              <h1 className=\"text-3xl font-bold text-gray-900\">Image Resizer</h1>\n              <p className=\"text-gray-600\">Resize, compress, convert, crop, and upscale your images</p>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Features Banner */}\n      <div className=\"bg-blue-50 border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n          <div className=\"flex flex-wrap justify-center gap-6 text-sm\">\n            <div className=\"flex items-center text-blue-700\">\n              <ArrowUpDown className=\"h-4 w-4 mr-2\" />\n              Resize Images\n            </div>\n            <div className=\"flex items-center text-blue-700\">\n              <Zap className=\"h-4 w-4 mr-2\" />\n              Compress Files\n            </div>\n            <div className=\"flex items-center text-blue-700\">\n              <Scissors className=\"h-4 w-4 mr-2\" />\n              Crop & Convert\n            </div>\n            <div className=\"flex items-center text-blue-700\">\n              <Maximize className=\"h-4 w-4 mr-2\" />\n              Upscale Quality\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* File Upload Section */}\n          <div className=\"bg-white p-6 rounded-lg shadow-lg\">\n            <h2 className=\"text-xl font-semibold mb-4\">Upload Images</h2>\n            <FileUpload onFilesSelected={handleFilesSelected} />\n          </div>\n\n          {/* Controls Section */}\n          <ImageControls\n            files={files}\n            onProcess={handleProcess}\n            isProcessing={isProcessing}\n          />\n        </div>\n\n        {/* Info Section */}\n        <div className=\"mt-12 bg-white rounded-lg shadow-lg p-6\">\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Features</h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            <div className=\"text-center\">\n              <ArrowUpDown className=\"h-12 w-12 text-blue-500 mx-auto mb-3\" />\n              <h3 className=\"font-semibold mb-2\">Smart Resize</h3>\n              <p className=\"text-sm text-gray-600\">Resize images to exact dimensions or by percentage while maintaining quality</p>\n            </div>\n            <div className=\"text-center\">\n              <Zap className=\"h-12 w-12 text-green-500 mx-auto mb-3\" />\n              <h3 className=\"font-semibold mb-2\">File Compression</h3>\n              <p className=\"text-sm text-gray-600\">Reduce file size to specific KB/MB targets without losing visual quality</p>\n            </div>\n            <div className=\"text-center\">\n              <Scissors className=\"h-12 w-12 text-purple-500 mx-auto mb-3\" />\n              <h3 className=\"font-semibold mb-2\">Crop & Convert</h3>\n              <p className=\"text-sm text-gray-600\">Crop images and convert between JPEG, PNG, WebP, and AVIF formats</p>\n            </div>\n            <div className=\"text-center\">\n              <Maximize className=\"h-12 w-12 text-red-500 mx-auto mb-3\" />\n              <h3 className=\"font-semibold mb-2\">AI Upscaling</h3>\n              <p className=\"text-sm text-gray-600\">Enhance image resolution up to 4x using advanced interpolation algorithms</p>\n            </div>\n          </div>\n        </div>\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-gray-800 text-white mt-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n          <div className=\"text-center\">\n            <p>&copy; 2024 Image Resizer. Professional image processing tool.</p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,sBAAsB,CAAC;QAC3B,SAAS;IACX;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI,MAAM,MAAM,KAAK,GAAG;QAExB,gBAAgB;QAEhB,IAAI;YACF,IAAI,MAAM,MAAM,KAAK,GAAG;gBACtB,yBAAyB;gBACzB,MAAM,kBAAkB,KAAK,CAAC,EAAE,EAAE;YACpC,OAAO;gBACL,mBAAmB;gBACnB,MAAM,kBAAkB,OAAO;YACjC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,MAAM;QACR,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,oBAAoB,OAAO,MAAY;QAC3C,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QACxB,SAAS,MAAM,CAAC,WAAW,QAAQ,OAAO,CAAC,QAAQ;QACnD,SAAS,MAAM,CAAC,UAAU,QAAQ,MAAM;QAExC,IAAI,QAAQ,KAAK,EAAE,SAAS,MAAM,CAAC,SAAS,QAAQ,KAAK,CAAC,QAAQ;QAClE,IAAI,QAAQ,MAAM,EAAE,SAAS,MAAM,CAAC,UAAU,QAAQ,MAAM,CAAC,QAAQ;QACrE,IAAI,QAAQ,UAAU,EAAE,SAAS,MAAM,CAAC,cAAc,QAAQ,UAAU,CAAC,QAAQ;QAEjF,IAAI,WAAW;QAEf,OAAQ,QAAQ,SAAS;YACvB,KAAK;gBACH,WAAW;gBACX,IAAI,QAAQ,QAAQ,EAAE;oBACpB,SAAS,MAAM,CAAC,KAAK,QAAQ,QAAQ,CAAC,CAAC,CAAC,QAAQ;oBAChD,SAAS,MAAM,CAAC,KAAK,QAAQ,QAAQ,CAAC,CAAC,CAAC,QAAQ;oBAChD,SAAS,MAAM,CAAC,SAAS,QAAQ,QAAQ,CAAC,KAAK,CAAC,QAAQ;oBACxD,SAAS,MAAM,CAAC,UAAU,QAAQ,QAAQ,CAAC,MAAM,CAAC,QAAQ;gBAC5D;gBACA;YACF,KAAK;gBACH,WAAW;gBACX,IAAI,QAAQ,KAAK,EAAE,SAAS,MAAM,CAAC,SAAS,QAAQ,KAAK,CAAC,QAAQ;gBAClE;YACF;gBACE,WAAW;QACf;QAEA,MAAM,WAAW,MAAM,MAAM,UAAU;YACrC,QAAQ;YACR,MAAM;QACR;QAEA,IAAI,SAAS,EAAE,EAAE;YACf,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,MAAM,MAAM,IAAI,eAAe,CAAC;YAChC,MAAM,IAAI,SAAS,aAAa,CAAC;YACjC,EAAE,IAAI,GAAG;YACT,EAAE,QAAQ,GAAG,CAAC,UAAU,EAAE,QAAQ,MAAM,EAAE;YAC1C,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,EAAE,KAAK;YACP,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,IAAI,eAAe,CAAC;QACtB,OAAO;YACL,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,MAAM,oBAAoB,OAAO,OAAe;QAC9C,MAAM,WAAW,IAAI;QAErB,MAAM,OAAO,CAAC,CAAA;YACZ,SAAS,MAAM,CAAC,SAAS;QAC3B;QAEA,SAAS,MAAM,CAAC,WAAW,QAAQ,OAAO,CAAC,QAAQ;QACnD,SAAS,MAAM,CAAC,UAAU,QAAQ,MAAM;QAExC,IAAI,QAAQ,KAAK,EAAE,SAAS,MAAM,CAAC,SAAS,QAAQ,KAAK,CAAC,QAAQ;QAClE,IAAI,QAAQ,MAAM,EAAE,SAAS,MAAM,CAAC,UAAU,QAAQ,MAAM,CAAC,QAAQ;QACrE,IAAI,QAAQ,UAAU,EAAE,SAAS,MAAM,CAAC,cAAc,QAAQ,UAAU,CAAC,QAAQ;QAEjF,MAAM,WAAW,MAAM,MAAM,qBAAqB;YAChD,QAAQ;YACR,MAAM;QACR;QAEA,IAAI,SAAS,EAAE,EAAE;YACf,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,MAAM,MAAM,IAAI,eAAe,CAAC;YAChC,MAAM,IAAI,SAAS,aAAa,CAAC;YACjC,EAAE,IAAI,GAAG;YACT,EAAE,QAAQ,GAAG;YACb,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,EAAE,KAAK;YACP,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,IAAI,eAAe,CAAC;QACtB,OAAO;YACL,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uMAAA,CAAA,QAAS;gCAAC,WAAU;;;;;;0CACrB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOrC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAG1C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGlC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;0BAQ7C,6LAAC;gBAAK,WAAU;;kCACd,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC,mIAAA,CAAA,UAAU;wCAAC,iBAAiB;;;;;;;;;;;;0CAI/B,6LAAC,sIAAA,CAAA,UAAa;gCACZ,OAAO;gCACP,WAAW;gCACX,cAAc;;;;;;;;;;;;kCAKlB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,6LAAC;gDAAG,WAAU;0DAAqB;;;;;;0DACnC,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAEvC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;0DACf,6LAAC;gDAAG,WAAU;0DAAqB;;;;;;0DACnC,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAEvC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;gDAAG,WAAU;0DAAqB;;;;;;0DACnC,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAEvC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;gDAAG,WAAU;0DAAqB;;;;;;0DACnC,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO7C,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;sCAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMf;GA/MwB;KAAA", "debugId": null}}]}