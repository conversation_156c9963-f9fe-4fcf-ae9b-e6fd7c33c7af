import { NextRequest, NextResponse } from 'next/server';
import sharp from 'sharp';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const x = parseInt(formData.get('x') as string) || 0;
    const y = parseInt(formData.get('y') as string) || 0;
    const width = parseInt(formData.get('width') as string);
    const height = parseInt(formData.get('height') as string);
    const format = formData.get('format') as string || 'jpeg';
    const quality = parseInt(formData.get('quality') as string) || 80;

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    if (!width || !height) {
      return NextResponse.json({ error: 'Width and height are required for cropping' }, { status: 400 });
    }

    const buffer = Buffer.from(await file.arrayBuffer());
    
    // Get original metadata
    const metadata = await sharp(buffer).metadata();
    
    // Perform crop operation
    let sharpInstance = sharp(buffer).extract({
      left: x,
      top: y,
      width: width,
      height: height
    });

    // Convert format and apply quality
    let outputBuffer: Buffer;
    switch (format.toLowerCase()) {
      case 'jpeg':
      case 'jpg':
        outputBuffer = await sharpInstance.jpeg({ quality }).toBuffer();
        break;
      case 'png':
        outputBuffer = await sharpInstance.png({ quality: Math.round(quality / 10) }).toBuffer();
        break;
      case 'webp':
        outputBuffer = await sharpInstance.webp({ quality }).toBuffer();
        break;
      case 'avif':
        outputBuffer = await sharpInstance.avif({ quality }).toBuffer();
        break;
      default:
        outputBuffer = await sharpInstance.jpeg({ quality }).toBuffer();
    }

    const outputMetadata = await sharp(outputBuffer).metadata();

    return new NextResponse(outputBuffer, {
      headers: {
        'Content-Type': `image/${format}`,
        'Content-Disposition': `attachment; filename="cropped.${format}"`,
        'X-Original-Size': metadata.size?.toString() || '0',
        'X-Output-Size': outputBuffer.length.toString(),
        'X-Original-Width': metadata.width?.toString() || '0',
        'X-Original-Height': metadata.height?.toString() || '0',
        'X-Output-Width': outputMetadata.width?.toString() || '0',
        'X-Output-Height': outputMetadata.height?.toString() || '0',
        'X-Crop-X': x.toString(),
        'X-Crop-Y': y.toString(),
        'X-Crop-Width': width.toString(),
        'X-Crop-Height': height.toString(),
      },
    });
  } catch (error) {
    console.error('Error cropping image:', error);
    return NextResponse.json({ error: 'Failed to crop image' }, { status: 500 });
  }
}
