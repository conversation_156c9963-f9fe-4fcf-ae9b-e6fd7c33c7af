'use client';

import React, { useState } from 'react';
import FileUpload from '@/components/FileUpload';
import ImageControls, { ProcessingOptions } from '@/components/ImageControls';
import { Image as ImageIcon, Zap, Scissors, ArrowUpDown, Maximize } from 'lucide-react';

export default function Home() {
  const [files, setFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);

  const handleFilesSelected = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
  };

  const handleProcess = async (options: ProcessingOptions) => {
    if (files.length === 0) return;

    setIsProcessing(true);

    try {
      if (files.length === 1) {
        // Single file processing
        await processSingleFile(files[0], options);
      } else {
        // Batch processing
        await processBatchFiles(files, options);
      }
    } catch (error) {
      console.error('Processing error:', error);
      alert('Error processing images. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const processSingleFile = async (file: File, options: ProcessingOptions) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('quality', options.quality.toString());
    formData.append('format', options.format);

    if (options.width) formData.append('width', options.width.toString());
    if (options.height) formData.append('height', options.height.toString());
    if (options.targetSize) formData.append('targetSize', options.targetSize.toString());

    let endpoint = '/api/resize';

    switch (options.operation) {
      case 'crop':
        endpoint = '/api/crop';
        if (options.cropData) {
          formData.append('x', options.cropData.x.toString());
          formData.append('y', options.cropData.y.toString());
          formData.append('width', options.cropData.width.toString());
          formData.append('height', options.cropData.height.toString());
        }
        break;
      case 'upscale':
        endpoint = '/api/upscale';
        if (options.scale) formData.append('scale', options.scale.toString());
        break;
      default:
        endpoint = '/api/resize';
    }

    const response = await fetch(endpoint, {
      method: 'POST',
      body: formData,
    });

    if (response.ok) {
      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `processed.${options.format}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } else {
      throw new Error('Failed to process image');
    }
  };

  const processBatchFiles = async (files: File[], options: ProcessingOptions) => {
    const formData = new FormData();

    files.forEach(file => {
      formData.append('files', file);
    });

    formData.append('quality', options.quality.toString());
    formData.append('format', options.format);

    if (options.width) formData.append('width', options.width.toString());
    if (options.height) formData.append('height', options.height.toString());
    if (options.targetSize) formData.append('targetSize', options.targetSize.toString());

    const response = await fetch('/api/batch-resize', {
      method: 'POST',
      body: formData,
    });

    if (response.ok) {
      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'processed_images.zip';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } else {
      throw new Error('Failed to process images');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center">
            <ImageIcon className="h-8 w-8 text-blue-500 mr-3" />
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Image Resizer</h1>
              <p className="text-gray-600">Resize, compress, convert, crop, and upscale your images</p>
            </div>
          </div>
        </div>
      </header>

      {/* Features Banner */}
      <div className="bg-blue-50 border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex flex-wrap justify-center gap-6 text-sm">
            <div className="flex items-center text-blue-700">
              <ArrowUpDown className="h-4 w-4 mr-2" />
              Resize Images
            </div>
            <div className="flex items-center text-blue-700">
              <Zap className="h-4 w-4 mr-2" />
              Compress Files
            </div>
            <div className="flex items-center text-blue-700">
              <Scissors className="h-4 w-4 mr-2" />
              Crop & Convert
            </div>
            <div className="flex items-center text-blue-700">
              <Maximize className="h-4 w-4 mr-2" />
              Upscale Quality
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* File Upload Section */}
          <div className="bg-white p-6 rounded-lg shadow-lg">
            <h2 className="text-xl font-semibold mb-4">Upload Images</h2>
            <FileUpload onFilesSelected={handleFilesSelected} />
          </div>

          {/* Controls Section */}
          <ImageControls
            files={files}
            onProcess={handleProcess}
            isProcessing={isProcessing}
          />
        </div>

        {/* Info Section */}
        <div className="mt-12 bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center">
              <ArrowUpDown className="h-12 w-12 text-blue-500 mx-auto mb-3" />
              <h3 className="font-semibold mb-2">Smart Resize</h3>
              <p className="text-sm text-gray-600">Resize images to exact dimensions or by percentage while maintaining quality</p>
            </div>
            <div className="text-center">
              <Zap className="h-12 w-12 text-green-500 mx-auto mb-3" />
              <h3 className="font-semibold mb-2">File Compression</h3>
              <p className="text-sm text-gray-600">Reduce file size to specific KB/MB targets without losing visual quality</p>
            </div>
            <div className="text-center">
              <Scissors className="h-12 w-12 text-purple-500 mx-auto mb-3" />
              <h3 className="font-semibold mb-2">Crop & Convert</h3>
              <p className="text-sm text-gray-600">Crop images and convert between JPEG, PNG, WebP, and AVIF formats</p>
            </div>
            <div className="text-center">
              <Maximize className="h-12 w-12 text-red-500 mx-auto mb-3" />
              <h3 className="font-semibold mb-2">AI Upscaling</h3>
              <p className="text-sm text-gray-600">Enhance image resolution up to 4x using advanced interpolation algorithms</p>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-gray-800 text-white mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <p>&copy; 2024 Image Resizer. Professional image processing tool.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
