'use client';

import React, { useState } from 'react';
import { Settings, Download, Loader2 } from 'lucide-react';

interface ImageControlsProps {
  files: File[];
  onProcess: (options: ProcessingOptions) => void;
  isProcessing: boolean;
}

export interface ProcessingOptions {
  width?: number;
  height?: number;
  quality: number;
  format: string;
  targetSize?: number;
  operation: 'resize' | 'compress' | 'convert' | 'crop' | 'upscale';
  cropData?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  scale?: number;
}

export default function ImageControls({ files, onProcess, isProcessing }: ImageControlsProps) {
  const [operation, setOperation] = useState<ProcessingOptions['operation']>('resize');
  const [width, setWidth] = useState<string>('');
  const [height, setHeight] = useState<string>('');
  const [quality, setQuality] = useState(80);
  const [format, setFormat] = useState('jpeg');
  const [targetSize, setTargetSize] = useState<string>('');
  const [scale, setScale] = useState(2);
  const [maintainAspectRatio, setMaintainAspectRatio] = useState(true);

  const handleProcess = () => {
    const options: ProcessingOptions = {
      quality,
      format,
      operation,
    };

    if (width) options.width = parseInt(width);
    if (height) options.height = parseInt(height);
    if (targetSize) options.targetSize = parseInt(targetSize);
    if (operation === 'upscale') options.scale = scale;

    onProcess(options);
  };

  const presetSizes = [
    { name: 'Instagram Square', width: 1080, height: 1080 },
    { name: 'Instagram Story', width: 1080, height: 1920 },
    { name: 'Facebook Cover', width: 1200, height: 630 },
    { name: 'Twitter Header', width: 1500, height: 500 },
    { name: 'YouTube Thumbnail', width: 1280, height: 720 },
    { name: 'HD', width: 1920, height: 1080 },
    { name: '4K', width: 3840, height: 2160 },
  ];

  const applyPreset = (preset: { width: number; height: number }) => {
    setWidth(preset.width.toString());
    setHeight(preset.height.toString());
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-lg">
      <div className="flex items-center mb-6">
        <Settings className="h-5 w-5 mr-2" />
        <h2 className="text-xl font-semibold">Processing Options</h2>
      </div>

      {/* Operation Selection */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Operation
        </label>
        <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
          {[
            { value: 'resize', label: 'Resize' },
            { value: 'compress', label: 'Compress' },
            { value: 'convert', label: 'Convert' },
            { value: 'crop', label: 'Crop' },
            { value: 'upscale', label: 'Upscale' },
          ].map((op) => (
            <button
              key={op.value}
              onClick={() => setOperation(op.value as ProcessingOptions['operation'])}
              className={`p-2 text-sm rounded-md border ${
                operation === op.value
                  ? 'bg-blue-500 text-white border-blue-500'
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
              }`}
            >
              {op.label}
            </button>
          ))}
        </div>
      </div>

      {/* Dimensions */}
      {(operation === 'resize' || operation === 'crop') && (
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Dimensions
          </label>
          <div className="grid grid-cols-2 gap-4 mb-3">
            <div>
              <label className="block text-xs text-gray-500 mb-1">Width (px)</label>
              <input
                type="number"
                value={width}
                onChange={(e) => setWidth(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
                placeholder="Auto"
              />
            </div>
            <div>
              <label className="block text-xs text-gray-500 mb-1">Height (px)</label>
              <input
                type="number"
                value={height}
                onChange={(e) => setHeight(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
                placeholder="Auto"
              />
            </div>
          </div>
          
          {operation === 'resize' && (
            <>
              <div className="mb-3">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={maintainAspectRatio}
                    onChange={(e) => setMaintainAspectRatio(e.target.checked)}
                    className="mr-2"
                  />
                  <span className="text-sm text-gray-700">Maintain aspect ratio</span>
                </label>
              </div>
              
              <div>
                <label className="block text-xs text-gray-500 mb-2">Quick Presets</label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {presetSizes.map((preset) => (
                    <button
                      key={preset.name}
                      onClick={() => applyPreset(preset)}
                      className="p-2 text-xs bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
                    >
                      {preset.name}
                      <br />
                      <span className="text-gray-500">{preset.width}×{preset.height}</span>
                    </button>
                  ))}
                </div>
              </div>
            </>
          )}
        </div>
      )}

      {/* Upscale Factor */}
      {operation === 'upscale' && (
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Scale Factor: {scale}x
          </label>
          <input
            type="range"
            min="1"
            max="4"
            step="0.1"
            value={scale}
            onChange={(e) => setScale(parseFloat(e.target.value))}
            className="w-full"
          />
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>1x</span>
            <span>2x</span>
            <span>3x</span>
            <span>4x</span>
          </div>
        </div>
      )}

      {/* Format Selection */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Output Format
        </label>
        <select
          value={format}
          onChange={(e) => setFormat(e.target.value)}
          className="w-full p-2 border border-gray-300 rounded-md"
        >
          <option value="jpeg">JPEG</option>
          <option value="png">PNG</option>
          <option value="webp">WebP</option>
          <option value="avif">AVIF</option>
        </select>
      </div>

      {/* Quality */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Quality: {quality}%
        </label>
        <input
          type="range"
          min="10"
          max="100"
          value={quality}
          onChange={(e) => setQuality(parseInt(e.target.value))}
          className="w-full"
        />
        <div className="flex justify-between text-xs text-gray-500 mt-1">
          <span>Low</span>
          <span>Medium</span>
          <span>High</span>
        </div>
      </div>

      {/* Target File Size */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Target File Size (KB) - Optional
        </label>
        <input
          type="number"
          value={targetSize}
          onChange={(e) => setTargetSize(e.target.value)}
          className="w-full p-2 border border-gray-300 rounded-md"
          placeholder="e.g., 500"
        />
        <p className="text-xs text-gray-500 mt-1">
          Leave empty to use quality setting only
        </p>
      </div>

      {/* Process Button */}
      <button
        onClick={handleProcess}
        disabled={files.length === 0 || isProcessing}
        className="w-full bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 text-white font-medium py-3 px-4 rounded-md transition-colors flex items-center justify-center"
      >
        {isProcessing ? (
          <>
            <Loader2 className="animate-spin h-4 w-4 mr-2" />
            Processing...
          </>
        ) : (
          <>
            <Download className="h-4 w-4 mr-2" />
            Process {files.length > 1 ? `${files.length} Images` : 'Image'}
          </>
        )}
      </button>
    </div>
  );
}
