'use client';

import React, { useState, useRef } from 'react';
import React<PERSON>rop, { Crop, PixelCrop } from 'react-image-crop';
import 'react-image-crop/dist/ReactCrop.css';
import { Download, RotateCcw } from 'lucide-react';

interface ImageCropperProps {
  file: File;
  onCrop: (cropData: { x: number; y: number; width: number; height: number }) => void;
  onClose: () => void;
}

export default function ImageCropper({ file, onCrop, onClose }: ImageCropperProps) {
  const [crop, setCrop] = useState<Crop>({
    unit: '%',
    width: 50,
    height: 50,
    x: 25,
    y: 25,
  });
  const [completedCrop, setCompletedCrop] = useState<PixelCrop>();
  const [imageUrl, setImageUrl] = useState<string>('');
  const imgRef = useRef<HTMLImageElement>(null);

  React.useEffect(() => {
    const url = URL.createObjectURL(file);
    setImageUrl(url);
    return () => URL.revokeObjectURL(url);
  }, [file]);

  const handleCropComplete = (crop: PixelCrop) => {
    setCompletedCrop(crop);
  };

  const handleApplyCrop = () => {
    if (completedCrop && imgRef.current) {
      onCrop({
        x: Math.round(completedCrop.x),
        y: Math.round(completedCrop.y),
        width: Math.round(completedCrop.width),
        height: Math.round(completedCrop.height),
      });
    }
  };

  const presetRatios = [
    { name: 'Free', aspect: undefined },
    { name: '1:1', aspect: 1 },
    { name: '4:3', aspect: 4 / 3 },
    { name: '16:9', aspect: 16 / 9 },
    { name: '3:2', aspect: 3 / 2 },
  ];

  const applyPresetRatio = (aspect: number | undefined) => {
    setCrop(prev => ({
      ...prev,
      aspect,
    }));
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">Crop Image</h2>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700"
            >
              ✕
            </button>
          </div>

          {/* Preset Ratios */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Aspect Ratio
            </label>
            <div className="flex flex-wrap gap-2">
              {presetRatios.map((ratio) => (
                <button
                  key={ratio.name}
                  onClick={() => applyPresetRatio(ratio.aspect)}
                  className={`px-3 py-1 text-sm rounded border ${
                    crop.aspect === ratio.aspect
                      ? 'bg-blue-500 text-white border-blue-500'
                      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  {ratio.name}
                </button>
              ))}
            </div>
          </div>

          {/* Crop Area */}
          <div className="mb-4 flex justify-center">
            {imageUrl && (
              <ReactCrop
                crop={crop}
                onChange={(_, percentCrop) => setCrop(percentCrop)}
                onComplete={handleCropComplete}
                aspect={crop.aspect}
                className="max-w-full max-h-96"
              >
                <img
                  ref={imgRef}
                  src={imageUrl}
                  alt="Crop preview"
                  className="max-w-full max-h-96 object-contain"
                />
              </ReactCrop>
            )}
          </div>

          {/* Crop Info */}
          {completedCrop && (
            <div className="mb-4 p-3 bg-gray-50 rounded text-sm">
              <p>
                Crop area: {Math.round(completedCrop.width)} × {Math.round(completedCrop.height)} px
                at ({Math.round(completedCrop.x)}, {Math.round(completedCrop.y)})
              </p>
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-end space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              onClick={() => {
                setCrop({
                  unit: '%',
                  width: 50,
                  height: 50,
                  x: 25,
                  y: 25,
                });
              }}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50 flex items-center"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset
            </button>
            <button
              onClick={handleApplyCrop}
              disabled={!completedCrop}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-300 flex items-center"
            >
              <Download className="h-4 w-4 mr-2" />
              Apply Crop
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
