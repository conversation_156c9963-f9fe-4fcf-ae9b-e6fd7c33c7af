import { NextRequest, NextResponse } from 'next/server';
import sharp from 'sharp';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const scale = parseFloat(formData.get('scale') as string) || 2;
    const format = formData.get('format') as string || 'jpeg';
    const quality = parseInt(formData.get('quality') as string) || 80;

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    if (scale < 1 || scale > 4) {
      return NextResponse.json({ error: 'Scale must be between 1 and 4' }, { status: 400 });
    }

    const buffer = Buffer.from(await file.arrayBuffer());
    
    // Get original metadata
    const metadata = await sharp(buffer).metadata();
    
    if (!metadata.width || !metadata.height) {
      return NextResponse.json({ error: 'Could not determine image dimensions' }, { status: 400 });
    }

    const newWidth = Math.round(metadata.width * scale);
    const newHeight = Math.round(metadata.height * scale);

    // Perform upscaling using different algorithms based on scale factor
    let sharpInstance = sharp(buffer);
    
    if (scale <= 2) {
      // Use bicubic interpolation for moderate upscaling
      sharpInstance = sharpInstance.resize(newWidth, newHeight, {
        kernel: sharp.kernel.cubic,
        fit: 'fill'
      });
    } else {
      // Use lanczos for higher quality upscaling
      sharpInstance = sharpInstance.resize(newWidth, newHeight, {
        kernel: sharp.kernel.lanczos3,
        fit: 'fill'
      });
    }

    // Apply sharpening for better results
    sharpInstance = sharpInstance.sharpen({
      sigma: 1,
      flat: 1,
      jagged: 2
    });

    // Convert format and apply quality
    let outputBuffer: Buffer;
    switch (format.toLowerCase()) {
      case 'jpeg':
      case 'jpg':
        outputBuffer = await sharpInstance.jpeg({ quality }).toBuffer();
        break;
      case 'png':
        outputBuffer = await sharpInstance.png({ quality: Math.round(quality / 10) }).toBuffer();
        break;
      case 'webp':
        outputBuffer = await sharpInstance.webp({ quality }).toBuffer();
        break;
      case 'avif':
        outputBuffer = await sharpInstance.avif({ quality }).toBuffer();
        break;
      default:
        outputBuffer = await sharpInstance.jpeg({ quality }).toBuffer();
    }

    const outputMetadata = await sharp(outputBuffer).metadata();

    return new NextResponse(outputBuffer, {
      headers: {
        'Content-Type': `image/${format}`,
        'Content-Disposition': `attachment; filename="upscaled.${format}"`,
        'X-Original-Size': metadata.size?.toString() || '0',
        'X-Output-Size': outputBuffer.length.toString(),
        'X-Original-Width': metadata.width?.toString() || '0',
        'X-Original-Height': metadata.height?.toString() || '0',
        'X-Output-Width': outputMetadata.width?.toString() || '0',
        'X-Output-Height': outputMetadata.height?.toString() || '0',
        'X-Scale-Factor': scale.toString(),
      },
    });
  } catch (error) {
    console.error('Error upscaling image:', error);
    return NextResponse.json({ error: 'Failed to upscale image' }, { status: 500 });
  }
}
